﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.14.36203.30 d17.14
MinimumVisualStudioVersion = 10.0.40219.1
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "LeakLocation", "LeakLocation.csproj", "{FE5E7FEA-03DE-40E4-B5F2-2C65F6DCC55E}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{FE5E7FEA-03DE-40E4-B5F2-2C65F6DCC55E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{FE5E7FEA-03DE-40E4-B5F2-2C65F6DCC55E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{FE5E7FEA-03DE-40E4-B5F2-2C65F6DCC55E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{FE5E7FEA-03DE-40E4-B5F2-2C65F6DCC55E}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {C10BF457-308C-4938-9EBD-00C7B8769B37}
	EndGlobalSection
EndGlobal
